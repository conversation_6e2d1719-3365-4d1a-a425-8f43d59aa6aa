package proxy

import (
	"fmt"

	"github.com/electricbubble/gadb"
	"github.com/electricbubble/gadb/proxy"
)

func StartAndroidProxy(port int, serial string, level LogLevel) error {
	c, err := gadb.NewClient()
	if err != nil {
		return fmt.Errorf("创建`adb`客户端失败，error: %s", err)
	}

	output, err := c.StartServerWithOutput()
	if err != nil {
		return fmt.Errorf("启动`adb`服务失败，output: %s, error: %s", output, err)
	} else if len(output) != 0 {
		defer func() {
			if c != nil {
				_ = c.KillServer()
			}
		}()
	}

	s := proxy.NewServer(c, serial, proxy.WithLoggerLevel(level.ToSLogLevel()))
	defer func() {
		if s != nil {
			_ = s.Close()
		}
	}()

	if err = s.ListenAndServe("tcp", fmt.Sprintf(":%d", port)); err != nil {
		return fmt.Errorf("监听端口失败，port: %d, error: %s", port, err)
	}

	return nil
}
