package device

import (
	"context"
	"testing"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

func TestAndroidDevice_GetAllIME(t *testing.T) {
	type args struct {
		ctx           context.Context
		deviceType    commonpb.DeviceType
		serial        string
		remoteAddress string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "",
			args: args{
				ctx:           context.Background(),
				deviceType:    commonpb.DeviceType_REAL_PHONE,
				serial:        "",
				remoteAddress: "",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				d, err := NewAndroidDevice(tt.args.ctx, tt.args.deviceType, tt.args.serial, tt.args.remoteAddress)
				if err != nil {
					t.Fatalf("failed to new android device, error: %+v", err)
				}
				defer func(d *AndroidDevice) {
					if d != nil {
						_ = d.Close()
					}
				}(d)

				got, err := d.GetAllIME()
				if (err != nil) != tt.wantErr {
					t.Errorf("GetAllIME() error = %v, wantErr %v", err, tt.wantErr)
					return
				}

				t.Logf("GetAllIME() got = %v", got)
			},
		)
	}
}
